# Stage 1: Builder - 의존성 설치 및 애플리케이션 코드 복사
FROM python:3.12-slim-bookworm AS builder

WORKDIR /app

# 시스템 패키지 매니저 업데이트 및 uv 설치
RUN apt-get update && \
    apt-get install -y curl && \
    pip install uv

# 가상 환경 생성 및 활성화
RUN python -m venv /app/.venv
ENV PATH="/app/.venv/bin:$PATH"

# pyproject.toml 복사 및 의존성 설치
COPY pyproject.toml requirements.lock* ./
RUN uv sync

# 애플리케이션 소스 코드 복사
COPY src/ ./src

# ----------------------------------------------------------------

# Stage 2: Final - 실제 실행 환경 구성
FROM python:3.12-slim-bookworm AS final

WORKDIR /app

# Chrome과 ChromeDriver 설치에 필요한 시스템 라이브러리 설치
RUN apt-get update && apt-get install -y --no-install-recommends \
    wget \
    unzip \
    ca-certificates \
    gnupg \
    jq \
    fonts-wqy-zenhei \
    # --- Chrome 실행에 필요한 핵심 라이브러리 ---
    libglib2.0-0 \
    libnss3 \
    libgconf-2-4 \
    libfontconfig1 \
    libdbus-1-3 \
    # ----------------------------------------- \
    && rm -rf /var/lib/apt/lists/*

# Google Chrome Stable 설치
# apt-key is deprecated. The recommended way is to dearmor the key and store it in /usr/share/keyrings
RUN wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | gpg --dearmor -o /usr/share/keyrings/google-chrome-keyring.gpg \
    && echo "deb [arch=amd64 signed-by=/usr/share/keyrings/google-chrome-keyring.gpg] http://dl.google.com/linux/chrome/deb/ stable main" > /etc/apt/sources.list.d/google-chrome.list \
    && apt-get update \
    && apt-get install -y google-chrome-stable --no-install-recommends \
    && rm -rf /var/lib/apt/lists/*

# ChromeDriver 설치 (설치된 Chrome 버전에 맞는 드라이버 자동 설치)
# jq is required for parsing JSON from the chrome-for-testing endpoint
RUN CHROME_BUILD_VERSION=$(google-chrome --version | cut -d ' ' -f 3 | cut -d '.' -f 1-3) && \
    FULL_DRIVER_VERSION=$(wget -qO- https://googlechromelabs.github.io/chrome-for-testing/latest-patch-versions-per-build.json | jq -r ".builds.\"${CHROME_BUILD_VERSION}\".version") && \
    if [ -z "$FULL_DRIVER_VERSION" ]; then \
      echo "ERROR: Could not find a matching ChromeDriver version for Chrome build ${CHROME_BUILD_VERSION}." >&2; \
      echo "Please check https://googlechromelabs.github.io/chrome-for-testing/." >&2; \
      exit 1; \
    fi && \
    DRIVER_URL="https://storage.googleapis.com/chrome-for-testing-public/${FULL_DRIVER_VERSION}/linux64/chromedriver-linux64.zip" && \
    echo "Downloading ChromeDriver version ${FULL_DRIVER_VERSION} from ${DRIVER_URL}" && \
    wget -q --continue -P /tmp/ ${DRIVER_URL} && \
    unzip -p /tmp/chromedriver-linux64.zip chromedriver-linux64/chromedriver > /usr/local/bin/chromedriver && \
    chmod +x /usr/local/bin/chromedriver && \
    rm /tmp/chromedriver-linux64.zip

# 보안을 위해 non-root 사용자 생성
RUN useradd --create-home --shell /bin/bash appuser

# Builder 스테이지에서 가상 환경과 소스 코드 복사 (소유자 지정)
COPY --from=builder --chown=appuser:appuser /app/.venv /app/.venv
COPY --from=builder --chown=appuser:appuser /app/src ./src

# 프로젝트 관련 파일 복사 및 필요 디렉토리 생성 (소유자 지정)
COPY --chown=appuser:appuser pyproject.toml README.md ./
RUN mkdir -p logs screenshots && chown -R appuser:appuser logs screenshots

# non-root 사용자로 전환
USER appuser

# 가상 환경 활성화 및 PYTHONPATH 설정
ENV PATH="/app/.venv/bin:$PATH"
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

EXPOSE 8080

# PATH에 venv가 포함되어 있으므로 uvicorn을 직접 실행
CMD ["uvicorn", "src.app:app", "--host", "0.0.0.0", "--port", "8080"]